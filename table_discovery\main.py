from pyspark.sql import SparkSession
from pyspark.sql.functions import collect_set, struct, col, when, lit
from collections import defaultdict, deque
import networkx as nx

# Configuration
base_path = "s3://metadata-ds-pdl/2025_11_03/data/"
main_table = "person"  # The root table with no dependencies
main_table_id = "person_id"  # The ID column name that other tables use to reference the main table

# Step 1: Find all directories and clean up trailing slashes
directories = [f.name.rstrip('/') for f in dbutils.fs.ls(base_path) if f.isDir()]
print(f"Found directories: {directories}")

# Step 2: Load each directory as a table
tables = {}
schemas = {}

for dir_name in directories:
    dir_path = f"{base_path}{dir_name}/"
    df = spark.read.parquet(dir_path)
    df.createOrReplaceTempView(dir_name)
    tables[dir_name] = df
    schemas[dir_name] = df.columns
    print(f"Loaded table '{dir_name}' with columns: {df.columns}")

# Step 3: Find all join conditions based on foreign key patterns
def find_join_conditions(schemas, main_table, main_table_id):
    """Find join conditions based on <table>_id pattern"""
    join_conditions = {}

    for table_name, columns in schemas.items():
        join_conditions[table_name] = []

        for col_name in columns:
            if col_name.endswith('_id') and col_name != 'id':
                # Extract referenced table name
                referenced_table = col_name[:-3]  # Remove '_id'

                # Check if this is a reference to the main table via main_table_id
                if col_name == main_table_id and main_table in schemas:
                    print(f"  Found main table reference: {table_name}.{col_name} -> {main_table}.{main_table_id}")
                    join_conditions[table_name].append({
                        'foreign_key': col_name,
                        'referenced_table': main_table,
                        'referenced_key': main_table_id  # Use main_table_id instead of 'id'
                    })
                # Check for regular table references
                elif referenced_table in schemas and 'id' in schemas[referenced_table]:
                    print(f"  Found regular reference: {table_name}.{col_name} -> {referenced_table}.id")
                    join_conditions[table_name].append({
                        'foreign_key': col_name,
                        'referenced_table': referenced_table,
                        'referenced_key': 'id'
                    })

    return join_conditions

join_conditions = find_join_conditions(schemas, main_table, main_table_id)
print("\nJoin conditions found:")
for table, conditions in join_conditions.items():
    for condition in conditions:
        print(f"  {table}.{condition['foreign_key']} -> {condition['referenced_table']}.{condition['referenced_key']}")

# Debug: Show which tables reference the main table
main_table_references = []
for table, conditions in join_conditions.items():
    for condition in conditions:
        if condition['referenced_table'] == main_table:
            main_table_references.append(f"{table}.{condition['foreign_key']}")

if main_table_references:
    print(f"\nTables referencing main table '{main_table}': {main_table_references}")
else:
    print(f"\nNo tables found referencing main table '{main_table}' via '{main_table_id}'")

# Step 4: Build dependency graph
def build_dependency_graph(join_conditions, main_table):
    """Build directed graph where edge A->B means A depends on B"""
    graph = nx.DiGraph()

    # Add all tables as nodes
    for table in schemas.keys():
        graph.add_node(table)

    # Add dependencies (edges)
    for table, conditions in join_conditions.items():
        for condition in conditions:
            referenced_table = condition['referenced_table']
            # Avoid self-loops (table depending on itself)
            if table != referenced_table:
                graph.add_edge(table, referenced_table)  # table depends on referenced_table
            else:
                print(f"WARNING: Skipping self-loop for table '{table}'")

    # Ensure main table is in the graph even if it has no dependencies
    if main_table not in graph:
        graph.add_node(main_table)

    return graph

dependency_graph = build_dependency_graph(join_conditions, main_table)

# Debug: Show dependency graph structure
print(f"\nDependency graph nodes: {list(dependency_graph.nodes())}")
print(f"Dependency graph edges: {list(dependency_graph.edges())}")

# Check for cycles in the dependency graph
if not nx.is_directed_acyclic_graph(dependency_graph):
    print("WARNING: Dependency graph contains cycles!")
    cycles = list(nx.simple_cycles(dependency_graph))
    for i, cycle in enumerate(cycles):
        print(f"  Cycle {i+1}: {' -> '.join(cycle + [cycle[0]])}")
else:
    print("✓ Dependency graph is acyclic")

# Step 5: Determine processing order (topological sort)
def get_processing_order(graph, main_table):
    """Get tables in order from most dependent to least dependent"""
    try:
        # Check for cycles first
        if not nx.is_directed_acyclic_graph(graph):
            print("Circular dependency detected!")
            cycles = list(nx.simple_cycles(graph))
            print(f"Cycles found: {cycles}")

            # Try to break cycles by removing edges that create them
            graph_copy = graph.copy()
            for cycle in cycles:
                if len(cycle) > 1:
                    # Remove the edge from the last to first node in the cycle
                    if graph_copy.has_edge(cycle[-1], cycle[0]):
                        print(f"Breaking cycle by removing edge: {cycle[-1]} -> {cycle[0]}")
                        graph_copy.remove_edge(cycle[-1], cycle[0])

            # Try topological sort on the modified graph
            if nx.is_directed_acyclic_graph(graph_copy):
                topo_order = list(nx.topological_sort(graph_copy))
                topo_order.reverse()
            else:
                print("Could not resolve all cycles, using simple ordering")
                # Fallback: put main table first, then others
                all_tables = list(graph.nodes())
                if main_table in all_tables:
                    all_tables.remove(main_table)
                    topo_order = all_tables + [main_table]
                else:
                    topo_order = all_tables
        else:
            # No cycles, proceed with normal topological sort
            topo_order = list(nx.topological_sort(graph))
            topo_order.reverse()

        # Ensure main_table is last if it exists
        if main_table in topo_order:
            topo_order.remove(main_table)
            topo_order.append(main_table)

        return topo_order
    except nx.NetworkXError as e:
        print(f"NetworkX error: {e}")
        # Fallback: put main table last, then others
        all_tables = list(graph.nodes())
        if main_table in all_tables:
            all_tables.remove(main_table)
            return all_tables + [main_table]
        else:
            return all_tables

processing_order = get_processing_order(dependency_graph, main_table)
print(f"\nProcessing order (most to least dependent): {processing_order}")

# Check for unused tables
def find_unused_tables(schemas, dependency_graph, main_table):
    """Find tables that are not connected to the dependency graph"""
    all_tables = set(schemas.keys())
    connected_tables = set(dependency_graph.nodes())
    unused_tables = all_tables - connected_tables

    # Also check for tables that have no dependencies and are not referenced by others
    isolated_tables = []
    for table in connected_tables:
        if table != main_table:
            # Check if table has no incoming or outgoing edges
            has_dependencies = dependency_graph.out_degree(table) > 0  # table depends on others
            is_referenced = dependency_graph.in_degree(table) > 0      # others depend on table

            if not has_dependencies and not is_referenced:
                isolated_tables.append(table)

    return unused_tables, isolated_tables

unused_tables, isolated_tables = find_unused_tables(schemas, dependency_graph, main_table)

if unused_tables:
    print(f"\n⚠️  WARNING: Tables not in dependency graph: {list(unused_tables)}")
    print("   These tables will not be included in the final result.")

if isolated_tables:
    print(f"\n⚠️  WARNING: Isolated tables (no dependencies, not referenced): {isolated_tables}")
    print("   These tables are in the graph but have no connections.")

# Step 6: Generate aggregation and join SQL
def generate_aggregation_sql(table_name, join_conditions, schemas):
    """Generate SQL to aggregate a table by its foreign keys"""
    conditions = join_conditions.get(table_name, [])
    
    if not conditions:
        # No dependencies, return as-is
        return f"SELECT * FROM {table_name}"
    
    # Get all non-id columns for aggregation
    non_id_columns = [col for col in schemas[table_name] 
                     if col != 'id' and not col.endswith('_id')]
    
    if not non_id_columns:
        # Only ID columns, just get distinct foreign keys
        foreign_keys = [cond['foreign_key'] for cond in conditions]
        return f"SELECT DISTINCT {', '.join(foreign_keys)} FROM {table_name}"
    
    # Build struct for collect_set - use named fields in struct
    struct_fields = ', '.join([f"{col} as {col}" for col in non_id_columns])
    foreign_keys = [cond['foreign_key'] for cond in conditions]

    # Add WHERE clause to filter out null foreign keys
    null_filters = [f"{fk} IS NOT NULL" for fk in foreign_keys]
    where_clause = f"WHERE {' AND '.join(null_filters)}" if null_filters else ""

    sql = f"""
    SELECT {', '.join(foreign_keys)},
           collect_set(struct({struct_fields})) as {table_name}_data
    FROM {table_name}
    {where_clause}
    GROUP BY {', '.join(foreign_keys)}
    """
    
    return sql

def generate_final_join_sql(processing_order, join_conditions, main_table, main_table_id):
    """Generate the final SQL query that joins all aggregated tables"""

    # Start with main table
    if main_table not in processing_order:
        raise ValueError(f"Main table '{main_table}' not found in processing order")

    # Track all columns to avoid duplicates
    selected_columns = []
    main_table_columns = [f"{main_table}.{col}" for col in schemas[main_table]]
    selected_columns.extend(main_table_columns)

    sql_parts = []
    processed_tables = {main_table}
    aggregate_queries = {}  # Store aggregate queries for output

    # Process tables in reverse order (least to most dependent for joining)
    for table in reversed(processing_order):
        if table == main_table or table in processed_tables:
            continue

        conditions = join_conditions.get(table, [])
        if not conditions:
            continue

        # Check if all referenced tables are already processed
        can_join = all(cond['referenced_table'] in processed_tables
                      for cond in conditions)

        if can_join:
            # Generate aggregated subquery
            agg_sql = generate_aggregation_sql(table, join_conditions, schemas)
            aggregate_queries[f"agg_{table}"] = agg_sql  # Store for output

            # Add aggregated data column to selection (avoid duplicates)
            agg_data_column = f"agg_{table}.{table}_data"
            if agg_data_column not in selected_columns:
                selected_columns.append(agg_data_column)

            # Build join conditions with null checks
            join_clauses = []
            for cond in conditions:
                ref_table = cond['referenced_table']
                foreign_key = cond['foreign_key']
                referenced_key = cond['referenced_key']

                # Use the correct referenced key (main_table_id for main table, 'id' for others)
                # Add null checks to avoid joining on null keys
                join_clauses.append(f"{ref_table}.{referenced_key} = agg_{table}.{foreign_key} AND {ref_table}.{referenced_key} IS NOT NULL AND agg_{table}.{foreign_key} IS NOT NULL")

            # Add to main query
            sql_parts.append(f"""
            LEFT JOIN (
                {agg_sql}
            ) agg_{table} ON {' AND '.join(join_clauses)}
            """)

            processed_tables.add(table)

    # Build final SELECT statement with explicit column list and null filter for main table
    main_table_key = main_table_id if main_table_id in schemas[main_table] else 'id'
    final_select = f"SELECT {', '.join(selected_columns)} FROM {main_table} WHERE {main_table}.{main_table_key} IS NOT NULL"
    if sql_parts:
        final_sql = final_select + '\n' + '\n'.join(sql_parts)
    else:
        final_sql = final_select

    # Debug: show selected columns
    print(f"\nSelected columns for final query: {selected_columns}")

    return final_sql, aggregate_queries

# Generate and execute the final SQL
try:
    final_sql, aggregate_queries = generate_final_join_sql(processing_order, join_conditions, main_table, main_table_id)

    print("\n" + "="*80)
    print("AGGREGATE TABLE QUERIES:")
    print("="*80)
    for agg_name, agg_query in aggregate_queries.items():
        print(f"\n-- {agg_name}:")
        print(agg_query.strip())
        print()

    print("\n" + "="*80)
    print("FINAL SQL QUERY:")
    print("="*80)
    print(final_sql)
    print("="*80)
    
    # Execute the query
    result_df = spark.sql(final_sql)
    result_df.createOrReplaceTempView("reconstructed_dataset")
    
    print(f"\nResult schema:")
    result_df.printSchema()
    
    print(f"\nResult preview:")
    result_df.show(5, truncate=False)
    
    print(f"\nTotal rows: {result_df.count()}")
    
except Exception as e:
    print(f"Error generating or executing SQL: {e}")
    
    # Fallback: show individual table aggregations
    print("\nFallback - Individual table aggregations:")
    for table in processing_order:
        if table != main_table:
            try:
                agg_sql = generate_aggregation_sql(table, join_conditions, schemas)
                print(f"\n{table} aggregation:")
                print(agg_sql)
                spark.sql(agg_sql).show(3)
            except Exception as table_error:
                print(f"Error with table {table}: {table_error}")