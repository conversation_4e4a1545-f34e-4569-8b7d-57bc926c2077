#!/usr/bin/env python3
"""
Test script to verify the dependency detection logic works correctly
without requiring Spark/Databricks environment.
"""

import networkx as nx

# Mock schemas for testing
test_schemas = {
    'person': ['id', 'name', 'email'],
    'address': ['id', 'person_id', 'street', 'city'],
    'phone': ['id', 'person_id', 'number', 'type'],
    'order': ['id', 'person_id', 'order_date', 'total'],
    'order_item': ['id', 'order_id', 'product_id', 'quantity'],
    'product': ['id', 'name', 'price'],
    'category': ['id', 'name'],
    'product_category': ['id', 'product_id', 'category_id']
}

# Configuration
main_table = "person"
main_table_id = "person_id"

def find_join_conditions(schemas, main_table, main_table_id):
    """Find join conditions based on <table>_id pattern"""
    join_conditions = {}
    
    for table_name, columns in schemas.items():
        join_conditions[table_name] = []
        
        for col_name in columns:
            if col_name.endswith('_id') and col_name != 'id':
                # Extract referenced table name
                referenced_table = col_name[:-3]  # Remove '_id'
                
                # Check if this is a reference to the main table via main_table_id
                if col_name == main_table_id and main_table in schemas:
                    join_conditions[table_name].append({
                        'foreign_key': col_name,
                        'referenced_table': main_table,
                        'referenced_key': 'id'
                    })
                # Check for regular table references
                elif referenced_table in schemas and 'id' in schemas[referenced_table]:
                    join_conditions[table_name].append({
                        'foreign_key': col_name,
                        'referenced_table': referenced_table,
                        'referenced_key': 'id'
                    })
    
    return join_conditions

def build_dependency_graph(join_conditions, main_table, schemas):
    """Build directed graph where edge A->B means A depends on B"""
    graph = nx.DiGraph()
    
    # Add all tables as nodes
    for table in schemas.keys():
        graph.add_node(table)
    
    # Add dependencies (edges)
    for table, conditions in join_conditions.items():
        for condition in conditions:
            referenced_table = condition['referenced_table']
            graph.add_edge(table, referenced_table)  # table depends on referenced_table
    
    # Ensure main table is in the graph even if it has no dependencies
    if main_table not in graph:
        graph.add_node(main_table)
    
    return graph

def test_dependency_detection():
    """Test the dependency detection logic"""
    print("Testing dependency detection logic...")
    print(f"Main table: {main_table}")
    print(f"Main table ID: {main_table_id}")
    print(f"Test schemas: {list(test_schemas.keys())}")
    
    # Find join conditions
    join_conditions = find_join_conditions(test_schemas, main_table, main_table_id)
    
    print("\nJoin conditions found:")
    for table, conditions in join_conditions.items():
        if conditions:
            for condition in conditions:
                print(f"  {table}.{condition['foreign_key']} -> {condition['referenced_table']}.{condition['referenced_key']}")
        else:
            print(f"  {table}: No dependencies")
    
    # Check main table references
    main_table_references = []
    for table, conditions in join_conditions.items():
        for condition in conditions:
            if condition['referenced_table'] == main_table:
                main_table_references.append(f"{table}.{condition['foreign_key']}")
    
    print(f"\nTables referencing main table '{main_table}': {main_table_references}")
    
    # Build dependency graph
    dependency_graph = build_dependency_graph(join_conditions, main_table, test_schemas)
    
    print(f"\nDependency graph nodes: {list(dependency_graph.nodes())}")
    print(f"Dependency graph edges: {list(dependency_graph.edges())}")
    
    # Check if main table is properly included
    if main_table in dependency_graph.nodes():
        print(f"✓ Main table '{main_table}' is included in the dependency graph")
    else:
        print(f"✗ Main table '{main_table}' is missing from the dependency graph")
    
    # Check if tables with person_id reference the main table
    expected_references = ['address', 'phone', 'order']
    actual_references = [table for table in main_table_references if any(ref.startswith(table + '.') for ref in main_table_references)]
    
    for expected in expected_references:
        if any(ref.startswith(expected + '.') for ref in main_table_references):
            print(f"✓ Table '{expected}' correctly references main table")
        else:
            print(f"✗ Table '{expected}' does not reference main table")
    
    return join_conditions, dependency_graph

if __name__ == "__main__":
    test_dependency_detection()
